import {
  Textarea,
} from "@fluentui/react-components";
import { Send24Regular } from "@fluentui/react-icons";
import "./MessageComposer.css";

export const MessageComposer = ({
  message,
  setMessage,
}: {
  message: string;
  setMessage: (message: string) => void;
}) => {
  const handleSend = () => {
    console.log(message);
    setMessage("");
  };

  return (
    <div className="message-composer">
      <Textarea
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        placeholder="Type your message..."
        className="message-composer-textarea"
      />
      <Send24Regular className="send-icon" onClick={handleSend} />
    </div>
  );
};