import {
  Textarea,
} from "@fluentui/react-components";
import { Send24Regular } from "@fluentui/react-icons";
import "./MessageComposer.css";

export const MessageComposer = ({
  message,
  setMessage,
  onSendMessage,
}: {
  message: string;
  setMessage: (message: string) => void;
  onSendMessage?: (message: string) => void;
}) => {
  const handleSend = () => {
    if (message.trim()) {
      if (onSendMessage) {
        onSendMessage(message);
      } else {
        console.log(message);
      }
      setMessage("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="message-composer">
      <div className="user-avatar">
        <img src="/Avatar_Hexagon.svg" alt="User Avatar" />
      </div>
      <div className="input-container">
        <Textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyPress}
          placeholder="Ask DataScout anything about your data..."
          className="message-composer-textarea"
          resize="none"
        />
        <Send24Regular
          className={`send-icon ${message.trim() ? 'active' : ''}`}
          onClick={handleSend}
        />
      </div>
    </div>
  );
};