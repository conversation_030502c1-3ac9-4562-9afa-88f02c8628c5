import { Card } from "@fluentui/react-components";
import "./WelcomeCard.css";

export const WelcomeCard = ({
  setMessage,
}: {
  setMessage: (message: string) => void;
}) => {
  const prompts = [
    "Show me last week's sales",
    "What are the top 5 products by revenue?",
    "Compare user engagement for Q1 vs Q2",
    "Forecast sales for the next 90 days",
  ];

  return (
    <Card className="card">
      <h1 className="title">Welcome to DataScout</h1>
      <p className="subtitle">
        I'm DataScout, your AI-powered data assistant. Here are some things you
        can ask me:
      </p>
      <div className="prompt-buttons">
        {prompts.map((prompt) => (
          <button
            key={prompt}
            className="prompt-button"
            onClick={() => setMessage(prompt)}
          >
            {prompt}
          </button>
        ))}
      </div>
    </Card>
  );
};