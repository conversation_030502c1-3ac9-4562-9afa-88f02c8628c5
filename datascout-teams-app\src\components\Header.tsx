import { Button } from "@fluentui/react-components";
import { Add24Regular } from "@fluentui/react-icons";
import "./Header.css";

export const Header = () => {
  return (
    <div className="header">
      <div className="header-left">
        <div className="logo">DataScout</div>
        <div className="status-indicator">
          <span className="status-dot"></span>
          <span className="status-text">AI Assistant Ready</span>
        </div>
      </div>
      <div className="header-right">
        <Button
          appearance="transparent"
          icon={<Add24Regular />}
          aria-label="New Chat"
          className="header-button"
        />
        <div className="user-profile">
          <img src="/Avatar_Square_Rounded.svg" alt="User Profile" className="profile-avatar" />
        </div>
      </div>
    </div>
  );
};