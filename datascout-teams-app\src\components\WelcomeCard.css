.card {
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    flex-grow: 1;
}

.title {
    font-size: 24px;
    font-weight: 600;
}

.subtitle {
    color: #6c757d;
}

.prompt-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    width: 100%;
}

.prompt-button {
    background-color: transparent;
    border: none;
    color: #0078d4;
    cursor: pointer;
    font-size: 14px;
    text-align: left;
    padding: 0;
}

.prompt-button:hover {
    text-decoration: underline;
}