.card {
    padding: 48px 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 32px;
    flex-grow: 1;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    margin: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e1e1e1;
    max-width: 800px;
    margin: 24px auto;
}

.title {
    font-size: 32px;
    font-weight: 600;
    color: #323130;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    text-align: center;
    margin: 0;
}

.subtitle {
    color: #605e5c;
    font-size: 16px;
    line-height: 1.5;
    text-align: center;
    max-width: 600px;
    margin: 0;
}

.prompt-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    width: 100%;
    max-width: 600px;
}

.prompt-button {
    background-color: #ffffff;
    border: 2px solid #e1e1e1;
    color: #323130;
    cursor: pointer;
    font-size: 14px;
    text-align: left;
    padding: 16px 20px;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.4;
    position: relative;
}

.prompt-button:hover {
    border-color: #0078d4;
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 120, 212, 0.15);
}

.prompt-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 120, 212, 0.2);
}

.prompt-button::before {
    content: "💡";
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.prompt-button:hover::before {
    opacity: 1;
}