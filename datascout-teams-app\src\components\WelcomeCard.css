.card {
    padding: 40px 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    flex-grow: 1;
    background-color: #ffffff;
    margin: 0;
    border-radius: 0;
    box-shadow: none;
    border: none;
    max-width: 600px;
    margin: 0 auto;
    justify-content: center;
}

.title {
    font-size: 28px;
    font-weight: 600;
    color: #323130;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    text-align: center;
    margin: 0;
}

.subtitle {
    color: #605e5c;
    font-size: 16px;
    line-height: 1.5;
    text-align: center;
    max-width: 600px;
    margin: 0;
}

.prompt-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    width: 100%;
    max-width: 500px;
}

.prompt-button {
    background-color: #f8f9fa;
    border: 1px solid #e1e1e1;
    color: #323130;
    cursor: pointer;
    font-size: 13px;
    text-align: left;
    padding: 12px 16px;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.3;
    position: relative;
}

.prompt-button:hover {
    border-color: #0078d4;
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.prompt-button:active {
    background-color: #f3f2f1;
}

.prompt-button::before {
    content: "→";
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.4;
    transition: opacity 0.2s ease;
    font-size: 12px;
    color: #605e5c;
}

.prompt-button:hover::before {
    opacity: 0.8;
    color: #0078d4;
}