.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background-color: #ffffff;
}

.messages-container {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 700px;
  margin: 0 auto;
}

.message {
  display: flex;
  gap: 12px;
  animation: fadeIn 0.3s ease-out;
}

.message-avatar {
  width: 28px;
  height: 28px;
  flex-shrink: 0;
  margin-top: 2px;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.bot-avatar {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.message-sender {
  font-weight: 600;
  font-size: 14px;
  color: #323130;
}

.message-time {
  font-size: 12px;
  color: #a19f9d;
}

.message-text {
  background-color: #ffffff;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #e1e1e1;
  font-size: 14px;
  line-height: 1.5;
  color: #323130;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  word-wrap: break-word;
}

.user-message .message-text {
  background-color: #deecf9;
  border-color: #c7e0f4;
  margin-left: auto;
  max-width: 70%;
}

.bot-message .message-text {
  background-color: #ffffff;
  border-color: #e1e1e1;
  max-width: 85%;
}

.user-message {
  flex-direction: row-reverse;
}

.user-message .message-header {
  flex-direction: row-reverse;
}

.user-message .message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.typing-indicator .message-avatar {
  width: 32px;
  height: 32px;
}

.typing-dots {
  background-color: #ffffff;
  border: 1px solid #e1e1e1;
  border-radius: 8px;
  padding: 16px 20px;
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-dot {
  width: 8px;
  height: 8px;
  background-color: #a19f9d;
  border-radius: 50%;
  animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typingDot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Scrollbar styling for messages */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 768px) {
  .messages-container {
    padding: 16px;
    gap: 16px;
  }
  
  .user-message .message-text {
    max-width: 85%;
  }
  
  .bot-message .message-text {
    max-width: 90%;
  }
}

@media (max-width: 480px) {
  .messages-container {
    padding: 12px;
  }
  
  .message-avatar {
    width: 28px;
    height: 28px;
  }
  
  .bot-avatar {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
  
  .message-text {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .user-message .message-text,
  .bot-message .message-text {
    max-width: 95%;
  }
}
