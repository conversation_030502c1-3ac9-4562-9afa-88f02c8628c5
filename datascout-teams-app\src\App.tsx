import { useState } from "react";
import { makeStyles, FluentProvider } from "@fluentui/react-components";
import { Header } from "./components/Header";
import { WelcomeCard } from "./components/WelcomeCard";
import { MessageComposer } from "./components/MessageComposer";
import { useTeamsTheme } from "./hooks/useTeamsTheme";

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100vh",
    width: "100vw",
  },
});

function App() {
  const styles = useStyles();
  const theme = useTeamsTheme();
  const [message, setMessage] = useState("");

  return (
    <FluentProvider theme={theme}>
      <div className={styles.container}>
        <Header />
        <WelcomeCard setMessage={setMessage} />
        <MessageComposer message={message} setMessage={setMessage} />
      </div>
    </FluentProvider>
  );
}

export default App;
