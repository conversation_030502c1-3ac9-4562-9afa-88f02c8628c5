import { useState } from "react";
import { makeStyles, FluentProvider } from "@fluentui/react-components";
import { Header } from "./components/Header";
import { WelcomeCard } from "./components/WelcomeCard";
import { MessageComposer } from "./components/MessageComposer";
import { ChatMessages, type Message } from "./components/ChatMessages";
import { useTeamsTheme } from "./hooks/useTeamsTheme";

const useStyles = makeStyles({
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100vh",
    width: "100vw",
  },
});

function App() {
  const styles = useStyles();
  const theme = useTeamsTheme();
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);

  const handleSendMessage = (messageText: string) => {
    if (messageText.trim()) {
      const userMessage: Message = {
        id: Date.now().toString(),
        text: messageText,
        sender: 'user',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, userMessage]);

      // Simulate bot response
      setTimeout(() => {
        const botMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: `I understand you're asking about: "${messageText}". I'm DataScout, your AI data assistant. I can help you analyze data, create visualizations, and provide insights. What specific data would you like me to help you with?`,
          sender: 'bot',
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, botMessage]);
      }, 1000);
    }
  };

  return (
    <FluentProvider theme={theme}>
      <div className={styles.container}>
        <Header />
        {messages.length === 0 ? (
          <WelcomeCard setMessage={setMessage} />
        ) : (
          <ChatMessages messages={messages} />
        )}
        <MessageComposer
          message={message}
          setMessage={setMessage}
          onSendMessage={handleSendMessage}
        />
      </div>
    </FluentProvider>
  );
}

export default App;
