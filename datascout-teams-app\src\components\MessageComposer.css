.message-composer {
    display: flex;
    align-items: flex-end;
    padding: 16px 24px 24px 24px;
    background-color: #ffffff;
    border-top: 1px solid #e1e1e1;
    gap: 12px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    margin-bottom: 6px;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.input-container {
    position: relative;
    flex: 1;
}

.message-composer-textarea {
    background-color: #ffffff;
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    padding: 12px 48px 12px 16px;
    width: 100%;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    min-height: 44px;
    max-height: 120px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.message-composer-textarea:focus {
    border-color: #0078d4;
    box-shadow: 0 0 0 1px #0078d4;
    outline: none;
}

.message-composer-textarea::placeholder {
    color: #a19f9d;
    font-style: italic;
}

.send-icon {
    position: absolute;
    right: 12px;
    bottom: 12px;
    cursor: pointer;
    color: #a19f9d;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    opacity: 0.6;
}

.send-icon.active {
    color: #0078d4;
    opacity: 1;
}

.send-icon:hover {
    background-color: #f3f2f1;
}

.send-icon.active:hover {
    background-color: #deecf9;
    color: #106ebe;
}

.send-icon:active {
    background-color: #edebe9;
}

.send-icon.active:active {
    background-color: #c7e0f4;
    color: #005a9e;
}