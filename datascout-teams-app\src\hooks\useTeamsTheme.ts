import { useEffect, useState } from "react";
import { app } from "@microsoft/teams-js";
import {
  webLightTheme,
  webDarkTheme,
  teamsHighContrastTheme,
  type Theme,
} from "@fluentui/react-components";

export const useTeamsTheme = () => {
  const [theme, setTheme] = useState(webLightTheme);

  useEffect(() => {
    app.initialize().then(() => {
      app.getContext().then((context) => {
        const theme = context.app.theme || "default";
        setTheme(getFluentTheme(theme));
        app.registerOnThemeChangeHandler((theme) => {
          setTheme(getFluentTheme(theme));
        });
      });
    });
  }, []);

  const getFluentTheme = (teamsTheme: string): Theme => {
    switch (teamsTheme) {
      case "dark":
        return webDarkTheme;
      case "contrast":
        return teamsHighContrastTheme;
      case "default":
      default:
        return webLightTheme;
    }
  };

  return theme;
};