.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #464775;
  padding: 12px 24px;
  color: white;
  border-bottom: 1px solid #e1e1e1;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  min-height: 56px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo {
  font-size: 18px;
  font-weight: 600;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo::before {
  content: "📊";
  font-size: 20px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  opacity: 0.9;
}

.status-dot {
  width: 8px;
  height: 8px;
  background-color: #92c5f7;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.status-text {
  color: #e1dfdd;
}

.header-button {
  color: white !important;
}

.header-button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.user-profile {
  width: 32px;
  height: 32px;
}

.profile-avatar {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: brightness(0) invert(1);
  opacity: 0.9;
}

.profile-avatar:hover {
  opacity: 1;
}