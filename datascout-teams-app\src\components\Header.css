.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  padding: 8px 16px;
  color: #323130;
  border-bottom: 1px solid #e1e1e1;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  min-height: 48px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo {
  font-size: 16px;
  font-weight: 600;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #323130;
}

.logo::before {
  content: "🔍";
  font-size: 18px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  opacity: 0.8;
}

.status-dot {
  width: 6px;
  height: 6px;
  background-color: #16c60c;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.status-text {
  color: #605e5c;
}

.header-button {
  color: #323130 !important;
}

.header-button:hover {
  background-color: #f3f2f1 !important;
}

.user-profile {
  width: 28px;
  height: 28px;
}

.profile-avatar {
  width: 100%;
  height: 100%;
  object-fit: contain;
  opacity: 0.8;
}

.profile-avatar:hover {
  opacity: 1;
}