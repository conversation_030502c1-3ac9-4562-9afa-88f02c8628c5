/* Global Styles for DataScout Teams App */

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #ffffff;
  color: #323130;
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #0078d4;
  outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus {
  outline: 2px solid #0078d4;
  outline-offset: 1px;
}

/* Animation utilities */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Typography utilities */
.text-primary {
  color: #323130;
}

.text-secondary {
  color: #605e5c;
}

.text-accent {
  color: #0078d4;
}

/* Spacing utilities */
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

/* Card utilities */
.card-shadow {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.card-shadow-hover {
  transition: box-shadow 0.2s ease;
}

.card-shadow-hover:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* Button utilities */
.btn-primary {
  background-color: #0078d4;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-family: inherit;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.btn-primary:hover {
  background-color: #106ebe;
}

.btn-primary:active {
  background-color: #005a9e;
}

.btn-secondary {
  background-color: transparent;
  color: #0078d4;
  border: 1px solid #0078d4;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-family: inherit;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background-color: #f3f2f1;
}

.btn-secondary:active {
  background-color: #edebe9;
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #0078d4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .message-composer {
    padding: 12px 16px 16px 16px;
  }
  
  .card {
    margin: 16px;
    padding: 32px 24px;
  }
  
  .header {
    padding: 8px 16px;
  }
  
  .prompt-buttons {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }
  
  .subtitle {
    font-size: 14px;
  }
  
  .prompt-button {
    padding: 12px 16px;
    font-size: 13px;
  }
}
